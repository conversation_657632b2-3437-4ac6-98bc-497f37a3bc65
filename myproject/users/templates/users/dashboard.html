{% load static %}
{% load currency_filters %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>QuickReceipt Dashboard</title>

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f9f9fb;
            font-family: 'Arial', sans-serif;
        }

        .sidebar {
            width: 250px;
            background-color: #ffffff;
            border-right: 1px solid #eaeaea;
            height: 100vh;
            position: fixed;
            padding: 20px;
        }

        .sidebar a {
            display: block;
            color: #333;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .sidebar a.active, .sidebar a:hover {
            background-color: #007bff;
            color: #ffffff;
        }

        .content {
            margin-left: 270px;
            padding: 20px;
        }

        .table-container {
            background: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .table th, .table td {
            vertical-align: middle;
            word-wrap: break-word;
            max-width: 200px;
        }

        /* Action buttons styling */
        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .action-buttons .btn {
            white-space: nowrap;
            min-width: 60px;
        }

        /* Responsive table */
        @media (max-width: 768px) {
            .table-container {
                padding: 15px;
                margin: 0 -10px;
                border-radius: 0;
            }

            .table {
                font-size: 14px;
            }

            .table th, .table td {
                padding: 8px 4px;
                max-width: 120px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 3px;
            }

            .action-buttons .btn {
                font-size: 12px;
                padding: 4px 8px;
                min-width: 50px;
            }
        }

        @media (max-width: 576px) {
            /* Stack table for very small screens */
            .table-responsive-stack {
                display: block;
            }

            .table-responsive-stack thead {
                display: none;
            }

            .table-responsive-stack tbody,
            .table-responsive-stack tr,
            .table-responsive-stack td {
                display: block;
                width: 100%;
            }

            .table-responsive-stack tr {
                border: 1px solid #ddd;
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 8px;
                background: #f9f9f9;
            }

            .table-responsive-stack td {
                border: none;
                padding: 5px 0;
                text-align: left;
                max-width: none;
            }

            .table-responsive-stack td:before {
                content: attr(data-label) ": ";
                font-weight: bold;
                color: #555;
            }

            .action-buttons {
                margin-top: 10px;
                justify-content: center;
            }
        }

        .filter-section {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 20px;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .filter-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
            min-width: 0;
        }

        .filter-group.search-group {
            flex: 1;
            min-width: 250px;
        }

        .filter-group.date-group {
            flex: 2;
            min-width: 300px;
        }

        .filter-group.button-group {
            flex: 0 0 auto;
            min-width: 150px;
        }

        .filter-section input, .filter-section select {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 14px;
        }

        .filter-section label {
            font-weight: 500;
            color: #555;
            white-space: nowrap;
        }

        .header {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        /* Responsive filter section */
        @media (max-width: 768px) {
            .filter-section {
                flex-direction: column;
                gap: 15px;
            }

            .filter-group {
                width: 100%;
                justify-content: space-between;
            }

            .filter-group.button-group {
                justify-content: center;
                gap: 10px;
            }

            .filter-group.button-group .btn {
                flex: 1;
                max-width: 120px;
            }
        }

        @media (max-width: 480px) {
            .filter-group.date-group {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group.date-group > div {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
            }

            .filter-group.date-group input[type="date"] {
                flex: 1;
            }
        }

        /* Mobile Navigation */
        .mobile-nav-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1050;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .mobile-nav .nav-link {
            display: block;
            color: #333;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .mobile-nav .nav-link:hover,
        .mobile-nav .nav-link.active {
            background-color: #007bff;
            color: #ffffff;
        }

        /* Hide sidebar for smaller screens and show mobile toggle */
        @media (max-width: 991px) {
            .sidebar {
                display: none;
            }
            .content {
                margin-left: 0;
                padding-top: 80px;
            }
        }

        @media (min-width: 992px) {
            .mobile-nav-toggle {
                display: none;
            }
        }

        /* Content overflow and text wrapping */
        .content {
            overflow-x: hidden;
        }

        .table-container .d-flex {
            flex-wrap: wrap;
            gap: 10px;
        }

        .table-container .d-flex small {
            word-break: break-word;
            max-width: 100%;
        }

        /* Modal Enhancements */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            border-radius: 15px 15px 0 0;
            border-bottom: 1px solid #dee2e6;
        }

        .modal-footer {
            border-top: 1px solid #dee2e6;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        .delete-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Animation for row removal */
        .fade-out {
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }

        /* Search and Filter Enhancements */
        .filter-section input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .search-highlight {
            background-color: #fff3cd;
            transition: background-color 0.3s ease;
        }

        .no-results-row {
            background-color: #f8f9fa;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }

        /* Loading state for filter button */
        .btn-loading {
            position: relative;
            color: transparent;
        }

        .btn-loading::after {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Navigation Toggle -->
    <div class="mobile-nav-toggle d-lg-none">
        <button class="btn btn-outline-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileSidebar" aria-controls="mobileSidebar">
            <span class="navbar-toggler-icon"></span> Menu
        </button>
    </div>

    <!-- Mobile Sidebar Offcanvas -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileSidebar" aria-labelledby="mobileSidebarLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="mobileSidebarLabel">QuickReceipt</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <nav class="mobile-nav">
                <a href="{% url 'welcome' %}" class="nav-link active">Dashboard</a>
                <a href="{% url 'generate_receipt' %}" class="nav-link">Generate Receipt</a>
                <a href="{% url 'update_profile' %}" class="nav-link">Profile</a>
                <a href="#" class="nav-link">Settings</a>
                <a href="#" class="nav-link">Logout</a>
            </nav>
        </div>
    </div>

    <div class="sidebar">
        <h2 class="mb-4">QuickReceipt</h2>
        <a href="{% url 'welcome' %}" class="active">Dashboard</a>
        <a href="{% url 'generate_receipt' %}">Generate Receipt</a>
        <a href="{% url 'update_profile' %}">Profile</a>
        <a href="#">Settings</a>
        <a href="#">Logout</a>
    </div>

    <div class="content">
        {% csrf_token %}
        {% include 'users/components/dashboard_nav.html' %}

        <div class="header">Welcome to your dashboard, {{ username }}</div>
        
        <!-- Filter Section -->
        <form method="GET" action="{% url 'dashboard' %}" id="filterForm">
            <div class="filter-section">
                <div class="filter-group search-group">
                    <label for="search">Search:</label>
                    <input type="text" id="search" name="search" placeholder="Search receipts..." value="{{ search_query }}">
                </div>
                <div class="filter-group date-group">
                    <div>
                        <label for="date_from">From:</label>
                        <input type="date" id="date_from" name="date_from" value="{{ date_from }}">
                    </div>
                    <div>
                        <label for="date_to">To:</label>
                        <input type="date" id="date_to" name="date_to" value="{{ date_to }}">
                    </div>
                </div>
                <div class="filter-group button-group">
                    <button type="submit" class="btn btn-primary" id="filterBtn">Filter</button>
                    <button type="button" class="btn btn-secondary" id="clearBtn">Clear</button>
                </div>
            </div>
        </form>

        <div class="table-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <p class="mb-0">Receipt generation history</p>
                {% if search_query or date_from or date_to %}
                    <small class="text-muted">
                        Showing {{ total_receipts }} result{{ total_receipts|pluralize }}
                        {% if search_query %}for "{{ search_query }}"{% endif %}
                        {% if date_from or date_to %}
                            {% if date_from and date_to %}
                                from {{ date_from }} to {{ date_to }}
                            {% elif date_from %}
                                from {{ date_from }}
                            {% elif date_to %}
                                until {{ date_to }}
                            {% endif %}
                        {% endif %}
                    </small>
                {% endif %}
            </div>
            {% if receipts %}
            <div class="table-responsive">
                <table class="table table-hover table-responsive-stack">
                    <thead class="table-light">
                    <tr>
                        <th>S/N</th>
                        <th>Customer Name</th>
                        <th>Payment Method</th>
                        <th>Date</th>
                        <th>Total</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                        {% for receipt in receipts %}
                        <tr>
                            <td data-label="S/N">{{ forloop.counter0|add:receipts.start_index }}</td>
                            <td data-label="Customer Name">{{ receipt.customer_name }}</td>
                            <td data-label="Payment Method">{{ receipt.payment_method }}</td>
                            <td data-label="Date">{{ receipt.date }}</td>
                            <td data-label="Total">{{ receipt.total|currency_format }}</td>
                            <td data-label="Action">
                                <div class="action-buttons">
                                    <a href="{% url 'view_receipt' receipt.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                    <button class="btn btn-sm btn-outline-danger delete-btn" data-receipt-id="{{ receipt.id }}" data-customer-name="{{ receipt.customer_name }}">Delete</button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                    <tr>
                        <td colspan="6" class="text-center">
                            {% if search_query or date_from or date_to %}
                                No receipts found matching your search criteria.
                                <br><small class="text-muted">Try adjusting your search terms or date range.</small>
                            {% else %}
                                No receipts found.
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                {% if search_query or date_from or date_to %}
                    <h5>No receipts found</h5>
                    <p class="text-muted">No receipts match your search criteria. Try adjusting your filters.</p>
                    <button type="button" class="btn btn-outline-primary" id="clearFiltersBtn">Clear All Filters</button>
                {% else %}
                    <h5>No receipts generated yet</h5>
                    <p class="text-muted">Start by generating your first receipt!</p>
                    <a href="{% url 'generate_receipt' %}" class="btn btn-primary">Generate Receipt</a>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the receipt for <strong id="customerNameSpan"></strong>?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete Receipt</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" id="messageModalHeader">
                    <h5 class="modal-title" id="messageModalLabel">Message</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p id="messageModalBody">Message content</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Handle delete button clicks
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('.delete-btn');
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            const messageModal = new bootstrap.Modal(document.getElementById('messageModal'));
            let currentReceiptId = null;
            let currentDeleteButton = null;

            // Function to show message modal
            function showMessage(title, message, isSuccess = true) {
                const modalHeader = document.getElementById('messageModalHeader');
                const modalTitle = document.getElementById('messageModalLabel');
                const modalBody = document.getElementById('messageModalBody');

                // Set header color based on success/error
                modalHeader.className = isSuccess ? 'modal-header bg-success text-white' : 'modal-header bg-danger text-white';
                modalTitle.textContent = title;
                modalBody.textContent = message;

                messageModal.show();
            }

            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    currentReceiptId = this.getAttribute('data-receipt-id');
                    const customerName = this.getAttribute('data-customer-name');
                    currentDeleteButton = this;

                    // Update modal content
                    document.getElementById('customerNameSpan').textContent = customerName;

                    // Show confirmation modal
                    deleteModal.show();
                });
            });

            // Handle confirm delete button
            document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
                if (!currentReceiptId) return;

                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
                                 document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                // Disable the button to prevent double-clicks
                this.disabled = true;
                this.textContent = 'Deleting...';

                // Make AJAX request to delete receipt
                fetch(`/auth/receipt/${currentReceiptId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken,
                    },
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    // Hide delete modal first
                    deleteModal.hide();

                    if (data.success) {
                        // Add fade-out animation to the row
                        const row = currentDeleteButton.closest('tr');
                        row.classList.add('fade-out');

                        // Remove the row after animation completes
                        setTimeout(() => {
                            row.remove();
                        }, 300);

                        // Show success message
                        showMessage('Success', 'Receipt deleted successfully!', true);

                        // Check if table is empty and reload if needed
                        const tableBody = document.querySelector('tbody');
                        if (tableBody && tableBody.children.length === 0) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        }
                    } else {
                        showMessage('Error', 'Error deleting receipt: ' + data.message, false);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    deleteModal.hide();
                    showMessage('Error', 'An error occurred while deleting the receipt.', false);
                })
                .finally(() => {
                    // Reset button state
                    this.disabled = false;
                    this.textContent = 'Delete Receipt';
                    currentReceiptId = null;
                    currentDeleteButton = null;
                });
            });

            // Search and Filter functionality
            const searchInput = document.getElementById('search');
            const clearBtn = document.getElementById('clearBtn');
            const clearFiltersBtn = document.getElementById('clearFiltersBtn');
            const filterForm = document.getElementById('filterForm');

            // Real-time search functionality (client-side)
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('tbody tr:not(.no-results)');

                    tableRows.forEach(row => {
                        const customerName = row.cells[1]?.textContent.toLowerCase() || '';
                        const paymentMethod = row.cells[2]?.textContent.toLowerCase() || '';
                        const date = row.cells[3]?.textContent.toLowerCase() || '';

                        if (customerName.includes(searchTerm) ||
                            paymentMethod.includes(searchTerm) ||
                            date.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });

                    // Check if any rows are visible
                    const visibleRows = Array.from(tableRows).filter(row => row.style.display !== 'none');
                    const emptyRow = document.querySelector('.no-results-row');

                    if (visibleRows.length === 0 && searchTerm && !emptyRow) {
                        // Add "no results" row
                        const tbody = document.querySelector('tbody');
                        const noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = `
                            <td colspan="6" class="text-center text-muted py-3">
                                No receipts match "${searchTerm}"
                            </td>
                        `;
                        tbody.appendChild(noResultsRow);
                    } else if (visibleRows.length > 0 && emptyRow) {
                        // Remove "no results" row
                        emptyRow.remove();
                    }
                });
            }

            // Clear filters functionality
            if (clearBtn) {
                clearBtn.addEventListener('click', function() {
                    // Clear form inputs
                    document.getElementById('search').value = '';
                    document.getElementById('date_from').value = '';
                    document.getElementById('date_to').value = '';

                    // Submit form to reload without filters
                    filterForm.submit();
                });
            }

            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', function() {
                    // Redirect to dashboard without any query parameters
                    window.location.href = '{% url "dashboard" %}';
                });
            }

            // Auto-submit form when date inputs change (optional)
            const dateInputs = document.querySelectorAll('#date_from, #date_to');
            dateInputs.forEach(input => {
                input.addEventListener('change', function() {
                    // Optional: Auto-submit when dates change
                    // Uncomment the line below if you want automatic filtering
                    // filterForm.submit();
                });
            });
        });
    </script>
</body>
</html>
